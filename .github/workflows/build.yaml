on:
  push:
    tags:
      - 'v*.*.*'
name: Build
jobs:
  build:
    strategy:
      matrix:
        go-version: [1.16.x]
        platform: [ubuntu-latest]
    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Set env
        run: echo RELEASE_VERSION=${GITHUB_REF:10} >> $GITHUB_ENV
      - name: Build
        run: docker build -t tomwright/mermaid-server:latest -t tomwright/mermaid-server:${{ env.RELEASE_VERSION }} -f Dockerfile .
      - name: Login
        run: echo ${{ secrets.DOCKER_PASS }} | docker login -u${{ secrets.DOCKER_USER }} --password-stdin
      - name: Push
        run: docker push tomwright/mermaid-server:latest && docker push tomwright/mermaid-server:${{ env.RELEASE_VERSION }}